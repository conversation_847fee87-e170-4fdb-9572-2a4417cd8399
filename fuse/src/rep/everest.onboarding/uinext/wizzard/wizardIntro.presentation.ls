package everest.onboarding

template presentation wizardIntro {

  state {
    currentQuestionIndex: Number<Int>
    totalQuestions: Number<Int>
    currentQuestionUuid: Text
  }

  mode main {
    on header allow view
    on question allow view
    on questionOrder allow view
    on options allow view, change
    on progress allow view
    on navigationButtons allow view
    allow actions nextQuestion, previousQuestion, initializeWizard, saveQuestionResponse
  }

  object data-source questionData {
    shape {
      child Header {
        title (editable: false): Text
        subtitle (editable: false): Text
      }

      child Question {
        text: field<OnboardingQuestion.text>
        title: field<OnboardingQuestion.title>
        uuid: field<OnboardingQuestion.uuid>
      }

      child Options {
        dynamic-fields
      }

      child QuestionOrder {
        questionOrderIndex: Number<Int>
        totalQuestionOrderIndex: Number<Int>
        currentQuestionIndex: Number<Int>
      }

      child Progress {
        currentStep (editable: false): Number<Int>
        totalSteps (editable: false): Number<Int>
        progressPercentage (editable: false): Number<Decimal>
        stepLabel (editable: false): Text
      }

      child NavigationButtons {
        canGoBack (editable: false): TrueFalse
        canContinue (editable: false): TrueFalse
        backLabel (editable: false): Text
        continueLabel (editable: false): Text
      }
    }

    modifications {
      on Options support update
    }

    routine nextQuestion {
      outputs {
        success: TrueFalse
        isLastQuestion: TrueFalse
        nextQuestionUuid: Text
      }
      properties {
        side-effects true
      }
    }

    routine previousQuestion {
      outputs {
        success: TrueFalse
        isFirstQuestion: TrueFalse
        previousQuestionUuid: Text
      }
      properties {
        side-effects false
      }
    }

    routine initializeWizard {
      outputs {
        currentQuestionIndex: Number<Int>
        totalQuestions: Number<Int>
        firstQuestionUuid: Text
      }
      properties {
        side-effects false
      }
    }

    routine saveQuestionResponse {
      inputs {
        questionUuid: Text
        selectedOptionValues: array<Text>
      }
      outputs {
        success: TrueFalse
        validationMessage: Text
      }
      properties {
        side-effects true
      }
    }
  }

  struct header {
    data questionData.Header
    fields *
  }

  struct question {
    data questionData.Question
    fields *
  }

  struct questionOrder {
    data questionData.QuestionOrder
    fields *
  }

  data-set options {
    component FieldGroup {
    }

    data questionData.Options
    fields *
  }

  struct progress {
    data questionData.Progress
    fields *
  }

  struct navigationButtons {
    data questionData.NavigationButtons
    fields *
  }

  delegate action nextQuestion to data-source<questionData>.nextQuestion

  delegate action previousQuestion to data-source<questionData>.previousQuestion

  delegate action initializeWizard to data-source<questionData>.initializeWizard

  delegate action saveQuestionResponse to data-source<questionData>.saveQuestionResponse
}
