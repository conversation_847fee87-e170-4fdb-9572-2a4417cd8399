import React, { useEffect, useState, useCallback } from 'react';
import { Button, Progress, Spin, message, Checkbox, Space } from 'antd';
import { useHistory } from 'react-router-dom';
import { Typography, Icon } from '@everestsystems/design-system';


const { H1, H2, P } = Typography;

// Everest logo URL
const EVEREST_LOGO = 'https://appdev.internal.everest-erp.com/assets/everest-logo-DHvidv0T.svg';

const DynamicQuestionPage: React.FC = () => {
  const [loading, setLoading] = useState<boolean>(true);
  const [selectedOptions, setSelectedOptions] = useState<string[]>([]);
  const [validationMessage, setValidationMessage] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const history = useHistory();

  // Use template presentation hook
  const {
    data,
    actions,
    refresh,
    isLoading: presentationLoading,
  } = useTemplatePresentation({
    urn: 'urn:evst:everest:onboarding:presentation:uinext/wizzard/wizardIntro',
    mode: 'main',
  });

  // Initialize wizard on component mount
  useEffect(() => {
    const initializeWizard = async () => {
      try {
        setLoading(true);
        await actions.initializeWizard();
        await refresh();
      } catch (error) {
        console.error('Error initializing wizard:', error);
        message.error('Failed to initialize wizard');
      } finally {
        setLoading(false);
      }
    };

    initializeWizard();
  }, []); // Empty dependency array - only run once on mount

  // Handle user name input change
  const handleUserNameChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setUserName(value);
    setValidationMessage(''); // Clear validation message on input change
  }, []);

  // Handle continue button click
  const handleContinue = useCallback(async () => {
    if (!userName.trim()) {
      setValidationMessage('Please enter your name to continue.');
      return;
    }

    if (userName.trim().length < 2) {
      setValidationMessage('Name must be at least 2 characters long.');
      return;
    }

    try {
      setIsSubmitting(true);
      setValidationMessage('');

      const result = await actions.continueToNext({ userName: userName.trim() });
      
      if (result.success) {
        // Navigate to next step - this would be Q2 (greetings page)
        // For now, we'll show a success message and stay on the same page
        message.success(`Nice to meet you ${userName}! Let's jump into work!`);
        // TODO: Navigate to next wizard step
        // history.push('/templates/everest.onboarding/uinext/wizzard/greetings');
      } else {
        setValidationMessage(result.validationMessage || 'Please check your input and try again.');
      }
    } catch (error) {
      console.error('Error continuing to next step:', error);
      message.error('Failed to proceed to next step');
    } finally {
      setIsSubmitting(false);
    }
  }, [userName, actions]);

  // Handle back button click
  const handleBack = useCallback(async () => {
    try {
      await actions.goBack();
      // Since this is the first step, going back might navigate to a different page
      // For now, we'll just show a message
      message.info('You are at the first step of the wizard');
    } catch (error) {
      console.error('Error going back:', error);
    }
  }, [actions]);

  // Handle Enter key press in input field
  const handleKeyDown = useCallback((e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === 'Enter') {
      handleContinue();
    }
  }, [handleContinue]);

  if (loading || presentationLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Spin size="large" />
      </div>
    );
  }

  const headerData = data?.header;
  const welcomeData = data?.welcomeContent;
  const userInputData = data?.userNameInput;
  const progressData = data?.progress;
  const navigationData = data?.navigationButtons;

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Progress Bar */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="max-w-4xl mx-auto">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700">Progress</span>
            <span className="text-sm text-gray-500">
              {progressData?.stepLabel || '1 of 9'}
            </span>
          </div>
          <Progress
            percent={progressData?.progressPercentage || 11.11}
            showInfo={false}
            strokeColor="#4F46E5"
            className="mb-0"
          />
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-6 py-8">
        <Spin spinning={isSubmitting}>
          {/* Header Section */}
          <div className="bg-blue-600 text-white rounded-lg p-8 mb-8">
            <div className="flex items-center space-x-4">
              <Icon name="user" size="large" className="text-white" />
              <div>
                <H1 className="text-white mb-0">
                  {headerData?.title || 'Everest ERP Setup Wizard'}
                </H1>
                <P className="text-blue-100 mb-0">
                  {headerData?.subtitle || 'Welcome to Everest Systems'}
                </P>
              </div>
            </div>
          </div>

          {/* Welcome Content */}
          <div className="bg-white rounded-lg shadow-sm p-8 mb-8">
            <div className="space-y-6">
              <div>
                <P className="text-lg text-gray-700 mb-4">
                  {welcomeData?.greeting || 'Hello, Welcome to the Everest systems.'}
                </P>
                <P className="text-lg text-gray-700 mb-4">
                  {welcomeData?.description || 'I am Onboarding Wizard and I will help you to set everything tailored to your needs.'}
                </P>
                <H2 className="text-xl font-semibold text-gray-900 mb-6">
                  {welcomeData?.question || 'What should I call you?'}
                </H2>
              </div>

              {/* User Input */}
              <div className="space-y-4">
                <Input
                  size="large"
                  placeholder={userInputData?.placeholder || 'Enter your name...'}
                  value={userName}
                  onChange={handleUserNameChange}
                  onKeyDown={handleKeyDown}
                  className="text-lg"
                  status={validationMessage ? 'error' : undefined}
                />
                {validationMessage && (
                  <P className="text-red-500 text-sm mb-0">{validationMessage}</P>
                )}
              </div>
            </div>
          </div>

          {/* Navigation */}
          <div className="flex justify-between items-center">
            <Button
              size="large"
              onClick={handleBack}
              disabled={!navigationData?.canGoBack}
              className={!navigationData?.canGoBack ? 'invisible' : ''}
            >
              <Icon name="arrow-left" className="mr-2" />
              {navigationData?.backLabel || 'Back'}
            </Button>

            <Button
              type="primary"
              size="large"
              onClick={handleContinue}
              disabled={!navigationData?.canContinue || isSubmitting}
              loading={isSubmitting}
            >
              {navigationData?.continueLabel || 'Continue'}
              <Icon name="arrow-right" className="ml-2" />
            </Button>
          </div>

          {/* Step Indicators */}
          <div className="flex justify-center mt-8">
            <div className="flex space-x-2">
              {Array.from({ length: progressData?.totalSteps || 9 }, (_, index) => (
                <div
                  key={index}
                  className={`w-2 h-2 rounded-full ${
                    index === (progressData?.currentStep || 1) - 1
                      ? 'bg-blue-600'
                      : 'bg-gray-300'
                  }`}
                />
              ))}
            </div>
          </div>
        </Spin>
      </div>
    </div>
  );
};

export default DynamicQuestionPage;
