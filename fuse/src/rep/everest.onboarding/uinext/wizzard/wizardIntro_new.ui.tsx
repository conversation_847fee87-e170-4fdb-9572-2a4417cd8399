import React, { useEffect, useState, useCallback } from 'react';
import { Button, Progress, Spin, message, Checkbox, Space } from 'antd';
import { useHistory } from 'react-router-dom';
import { Typography, Icon } from '@everestsystems/design-system';
import { useTemplatePresentation } from '@everestsystems/content-core/lib/react';

const { H1, H2, P } = Typography;

const DynamicQuestionPage: React.FC = () => {
  const [loading, setLoading] = useState<boolean>(true);
  const [selectedOptions, setSelectedOptions] = useState<string[]>([]);
  const [validationMessage, setValidationMessage] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState<boolean>(false);
  const history = useHistory();

  // Use template presentation hook
  const {
    data,
    actions,
    refresh,
    isLoading: presentationLoading,
  } = useTemplatePresentation({
    urn: 'urn:evst:everest:onboarding:presentation:uinext/wizzard/wizardIntro',
    mode: 'main',
  });

  // Initialize wizard on component mount
  useEffect(() => {
    const initializeWizard = async () => {
      try {
        setLoading(true);
        await actions.initializeWizard();
        await refresh();
      } catch (error) {
        console.error('Error initializing wizard:', error);
        message.error('Failed to initialize wizard');
      } finally {
        setLoading(false);
      }
    };

    initializeWizard();
  }, []); // Empty dependency array - only run once on mount

  // Update selected options when data changes
  useEffect(() => {
    if (data?.options) {
      const selected: string[] = [];
      Object.keys(data.options).forEach(key => {
        if (data.options[key]) {
          selected.push(key);
        }
      });
      setSelectedOptions(selected);
    }
  }, [data?.options]);

  // Handle option selection change
  const handleOptionChange = useCallback((optionId: string, checked: boolean) => {
    let newSelectedOptions: string[];
    
    if (checked) {
      newSelectedOptions = [...selectedOptions, optionId];
    } else {
      newSelectedOptions = selectedOptions.filter(id => id !== optionId);
    }
    
    setSelectedOptions(newSelectedOptions);
    setValidationMessage(''); // Clear validation message on selection change
    
    // Update the presentation layer
    if (actions.updateOptions) {
      actions.updateOptions({ fieldName: optionId, newFieldValue: checked }).catch((error: any) => {
        console.error('Error updating option:', error);
      });
    }
  }, [selectedOptions, actions]);

  // Handle continue to next question
  const handleContinue = useCallback(async () => {
    if (selectedOptions.length === 0) {
      setValidationMessage('Please select at least one option to continue.');
      return;
    }

    try {
      setIsSubmitting(true);
      setValidationMessage('');

      const result = await actions.nextQuestion();
      
      if (result.success) {
        if (result.isLastQuestion) {
          message.success('You have completed all questions!');
          // Navigate to completion page or next step
          // history.push('/templates/everest.onboarding/uinext/steps/steps');
        } else {
          message.success('Moving to next question...');
          setSelectedOptions([]); // Reset selections for next question
          await refresh(); // Refresh to load next question
        }
      } else {
        setValidationMessage('Please check your selections and try again.');
      }
    } catch (error) {
      console.error('Error continuing to next question:', error);
      message.error('Failed to proceed to next question');
    } finally {
      setIsSubmitting(false);
    }
  }, [selectedOptions, actions, refresh, history]);

  // Handle back to previous question
  const handleBack = useCallback(async () => {
    try {
      setIsSubmitting(true);
      const result = await actions.previousQuestion();
      
      if (result.success) {
        if (result.isFirstQuestion) {
          message.info('You are at the first question');
        } else {
          message.success('Moving to previous question...');
          setSelectedOptions([]); // Reset selections for previous question
          await refresh(); // Refresh to load previous question
        }
      }
    } catch (error) {
      console.error('Error going back:', error);
      message.error('Failed to go back to previous question');
    } finally {
      setIsSubmitting(false);
    }
  }, [actions, refresh]);

  if (loading || presentationLoading) {
    return (
      <div className="flex justify-center items-center h-screen">
        <Spin size="large" />
      </div>
    );
  }

  const headerData = data?.header;
  const questionData = data?.question;
  const optionsData = data?.options;
  const progressData = data?.progress;
  const navigationData = data?.navigationButtons;

  // Render options dynamically
  const renderOptions = () => {
    if (!optionsData) return null;

    return Object.keys(optionsData).map((optionId) => {
      const isSelected = selectedOptions.includes(optionId);
      const optionLabel = data?.optionsMetadata?.[optionId]?.label || optionId;

      return (
        <div key={optionId} className="border border-gray-200 rounded-lg p-4 hover:border-blue-300 transition-colors">
          <Checkbox
            checked={isSelected}
            onChange={(e) => handleOptionChange(optionId, e.target.checked)}
            className="text-lg"
          >
            {optionLabel}
          </Checkbox>
        </div>
      );
    });
  };

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Progress Bar */}
      <div className="bg-white border-b border-gray-200 px-6 py-4">
        <div className="max-w-4xl mx-auto">
          <div className="flex justify-between items-center mb-2">
            <span className="text-sm font-medium text-gray-700">Progress</span>
            <span className="text-sm text-gray-500">
              {progressData?.stepLabel || '1 of 9'}
            </span>
          </div>
          <Progress
            percent={progressData?.progressPercentage || 11.11}
            showInfo={false}
            strokeColor="#4F46E5"
            className="mb-0"
          />
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-6 py-8">
        <Spin spinning={isSubmitting}>
          {/* Header Section */}
          <div className="bg-blue-600 text-white rounded-lg p-8 mb-8">
            <div className="flex items-center space-x-4">
              <Icon name="help-circle" size="large" className="text-white" />
              <div>
                <H1 className="text-white mb-0">
                  {headerData?.title || 'Everest ERP Setup Wizard'}
                </H1>
                <P className="text-blue-100 mb-0">
                  {headerData?.subtitle || 'Dynamic Question System'}
                </P>
              </div>
            </div>
          </div>

          {/* Question Content */}
          <div className="bg-white rounded-lg shadow-sm p-8 mb-8">
            <div className="space-y-6">
              <div>
                <H2 className="text-2xl font-semibold text-gray-900 mb-2">
                  {questionData?.title || 'Question Title'}
                </H2>
                <P className="text-lg text-gray-700 mb-6">
                  {questionData?.text || 'Question text will appear here...'}
                </P>
              </div>

              {/* Dynamic Options */}
              <div className="space-y-4">
                <Space direction="vertical" className="w-full" size="large">
                  {renderOptions()}
                </Space>
                {validationMessage && (
                  <P className="text-red-500 text-sm mb-0">{validationMessage}</P>
                )}
              </div>
            </div>
          </div>

          {/* Navigation */}
          <div className="flex justify-between items-center">
            <Button
              size="large"
              onClick={handleBack}
              disabled={!navigationData?.canGoBack || isSubmitting}
            >
              <Icon name="arrow-left" className="mr-2" />
              {navigationData?.backLabel || 'Back'}
            </Button>

            <Button
              type="primary"
              size="large"
              onClick={handleContinue}
              disabled={!navigationData?.canContinue || isSubmitting}
              loading={isSubmitting}
            >
              {navigationData?.continueLabel || 'Continue'}
              <Icon name="arrow-right" className="ml-2" />
            </Button>
          </div>

          {/* Step Indicators */}
          <div className="flex justify-center mt-8">
            <div className="flex space-x-2">
              {Array.from({ length: progressData?.totalSteps || 9 }, (_, index) => (
                <div
                  key={index}
                  className={`w-2 h-2 rounded-full ${
                    index === (progressData?.currentStep || 1) - 1
                      ? 'bg-blue-600'
                      : index < (progressData?.currentStep || 1) - 1
                      ? 'bg-blue-400'
                      : 'bg-gray-300'
                  }`}
                />
              ))}
            </div>
          </div>
        </Spin>
      </div>
    </div>
  );
};

export default DynamicQuestionPage;
