import { DATA, DYNAMIC_FIELDS, METADATA } from '@everestsystems/content-core';
import { OnboardingQuestion } from '@pkg/everest.onboarding/types/OnboardingQuestion';
import { OnboardingQuestionOption } from '@pkg/everest.onboarding/types/OnboardingQuestionOption';
import type { wizardIntroPresentation } from '@pkg/everest.onboarding/types/presentations/uinext/wizzard/wizardIntro';
import { set } from 'lodash';

class QuestionDataSource
  implements wizardIntroPresentation.dataSources.questionData.implementation
{
  private questionOrderIndex = 1;
  private totalQuestionOrderIndex: number | undefined = undefined;
  private question: { text: string; title: string; uuid: string } | undefined = undefined;
  private optionsData: Record<string, boolean> = {};
  private optionsMetadata: any = {};

  public async query({
    session,
  }: wizardIntroPresentation.dataSources.questionData.callbacks.query.input): Promise<wizardIntroPresentation.dataSources.questionData.callbacks.query.combinedOutput> {
    // Get total questions count if not already loaded
    if (this.totalQuestionOrderIndex === undefined) {
      const questionData = await OnboardingQuestion.query(
        session,
        { orderBy: [{ field: 'orderIndex', ordering: 'desc' }], take: 1 },
        ['orderIndex']
      );
      this.totalQuestionOrderIndex = questionData?.[0]?.orderIndex || 0;
    }

    // Load current question if not already loaded
    if (this.question === undefined) {
      const questions = await OnboardingQuestion.query(
        session,
        { where: { orderIndex: this.questionOrderIndex } },
        ['text', 'title', 'uuid']
      );

      if (questions.length > 0) {
        this.question = {
          text: questions[0].text || '',
          title: questions[0].title || '',
          uuid: questions[0].uuid || '',
        };

        // Load options for this question
        const options = await OnboardingQuestionOption.query(
          session,
          { where: { onboardingQuestionUUID: questions[0].uuid } },
          ['value', 'orderIndex', 'uuid', 'isSelected']
        );

        this.optionsData = {};
        this.optionsMetadata = {
          [DYNAMIC_FIELDS]: {},
        };

        // Load saved answers from localStorage
        const savedAnswers = this.loadAnswersFromLocalStorage(questions[0].uuid || '');

        for (const option of options) {
          const fieldName = `${option.uuid}`;
          // Use saved answer if available, otherwise use database value
          const isSelected = savedAnswers[option.uuid || ''] !== undefined
            ? savedAnswers[option.uuid || '']
            : option.isSelected || false;

          this.optionsData[fieldName] = isSelected;
          this.optionsMetadata[DYNAMIC_FIELDS][fieldName] = {
            type: {
              urn: 'urn:evst:everest:onboarding:primitive:QuestionCheckbox',
            },
            label: option.value || '',
          };
        }
      }
    }

    const progressPercentage = this.totalQuestionOrderIndex > 0
      ? Number(((this.questionOrderIndex / this.totalQuestionOrderIndex) * 100).toFixed(2))
      : 0;

    return {
      [DATA]: {
        Header: {
          title: 'Everest ERP Setup Wizard',
          subtitle: 'Dynamic Question System',
        },
        Question: this.question,
        Options: this.optionsData,
        QuestionOrder: {
          questionOrderIndex: this.questionOrderIndex,
          totalQuestionOrderIndex: this.totalQuestionOrderIndex,
          currentQuestionIndex: this.questionOrderIndex,
        },
        Progress: {
          currentStep: this.questionOrderIndex,
          totalSteps: this.totalQuestionOrderIndex || 1,
          progressPercentage: progressPercentage as any,
          stepLabel: `${this.questionOrderIndex} of ${this.totalQuestionOrderIndex || 1}`,
        },
        NavigationButtons: {
          canGoBack: this.questionOrderIndex > 1,
          canContinue: true,
          backLabel: 'Back',
          continueLabel: this.questionOrderIndex >= (this.totalQuestionOrderIndex || 1) ? 'Finish' : 'Continue',
        },
      },
      [METADATA]: {
        Options: this.optionsMetadata,
      },
    };
  }

  // Helper method to load answers from localStorage
  private loadAnswersFromLocalStorage(questionUuid: string): Record<string, boolean> {
    try {
      const savedAnswers = localStorage.getItem(`onboarding_answers_${questionUuid}`);
      return savedAnswers ? JSON.parse(savedAnswers) : {};
    } catch (error) {
      console.error('Error loading answers from localStorage:', error);
      return {};
    }
  }

  // Helper method to save answers to localStorage
  private saveAnswersToLocalStorage(questionUuid: string, answers: Record<string, boolean>): void {
    try {
      localStorage.setItem(`onboarding_answers_${questionUuid}`, JSON.stringify(answers));
    } catch (error) {
      console.error('Error saving answers to localStorage:', error);
    }
  }

  public async update_Options({
    fieldName,
    newFieldValue,
  }: wizardIntroPresentation.dataSources.questionData.callbacks.update_Options.input): Promise<wizardIntroPresentation.dataSources.questionData.callbacks.update_Options.output> {
    // Update the option selection
    set(this, ['optionsData', fieldName], newFieldValue);

    // Save to localStorage if we have a current question
    if (this.question?.uuid) {
      const currentAnswers: Record<string, boolean> = {};
      currentAnswers[fieldName] = newFieldValue as boolean;
      this.saveAnswersToLocalStorage(this.question.uuid, {
        ...this.loadAnswersFromLocalStorage(this.question.uuid),
        ...currentAnswers,
      });
    }
  }

  public async execute_nextQuestion({
    session,
  }: wizardIntroPresentation.dataSources.questionData.routines.nextQuestion.executeInput): Promise<wizardIntroPresentation.dataSources.questionData.routines.nextQuestion.executeOutput> {
    // Save current answers to localStorage
    if (this.question?.uuid) {
      const answers: Record<string, boolean> = {};
      Object.keys(this.optionsData).forEach(key => {
        answers[key] = this.optionsData[key];
      });
      this.saveAnswersToLocalStorage(this.question.uuid, answers);
    }

    // Move to next question
    this.questionOrderIndex = this.questionOrderIndex + 1;
    this.question = undefined; // Reset to force reload

    const isLastQuestion = this.questionOrderIndex > (this.totalQuestionOrderIndex || 1);

    return {
      success: true,
      isLastQuestion,
      nextQuestionUuid: '', // Will be loaded in next query
    };
  }

  public async execute_previousQuestion({}: wizardIntroPresentation.dataSources.questionData.routines.previousQuestion.executeInput): Promise<wizardIntroPresentation.dataSources.questionData.routines.previousQuestion.executeOutput> {
    // Save current answers to localStorage
    if (this.question?.uuid) {
      const answers: Record<string, boolean> = {};
      Object.keys(this.optionsData).forEach(key => {
        answers[key] = this.optionsData[key];
      });
      this.saveAnswersToLocalStorage(this.question.uuid, answers);
    }

    // Move to previous question
    if (this.questionOrderIndex > 1) {
      this.questionOrderIndex = this.questionOrderIndex - 1;
      this.question = undefined; // Reset to force reload
    }

    const isFirstQuestion = this.questionOrderIndex <= 1;

    return {
      success: true,
      isFirstQuestion,
      previousQuestionUuid: '', // Will be loaded in next query
    };
  }

  public async execute_initializeWizard({}: wizardIntroPresentation.dataSources.questionData.routines.initializeWizard.executeInput): Promise<wizardIntroPresentation.dataSources.questionData.routines.initializeWizard.executeOutput> {
    // Initialize wizard state
    this.questionOrderIndex = 1;
    this.totalQuestionOrderIndex = undefined;
    this.question = undefined;

    return {
      currentQuestionIndex: this.questionOrderIndex,
      totalQuestions: this.totalQuestionOrderIndex || 0,
      firstQuestionUuid: '', // Will be loaded in query
    };
  }

  public async execute_saveQuestionResponse({
    input,
  }: wizardIntroPresentation.dataSources.questionData.routines.saveQuestionResponse.executeInput): Promise<wizardIntroPresentation.dataSources.questionData.routines.saveQuestionResponse.executeOutput> {
    const { questionUuid, selectedOptionValues } = input;

    try {
      // Save to localStorage
      const answers: Record<string, boolean> = {};
      selectedOptionValues.forEach(value => {
        answers[value] = true;
      });
      this.saveAnswersToLocalStorage(questionUuid, answers);

      return {
        success: true,
        validationMessage: '',
      };
    } catch (error) {
      console.error('Error saving question response:', error);
      return {
        success: false,
        validationMessage: 'Failed to save your response. Please try again.',
      };
    }
  }
}

export default {
  questionData() {
    return new QuestionDataSource();
  },
} satisfies wizardIntroPresentation.implementation;
